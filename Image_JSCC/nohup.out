WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
E0000 00:00:1754288239.862776  225779 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
E0000 00:00:1754288239.873576  225779 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
W0000 00:00:1754288239.900207  225779 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1754288239.900253  225779 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1754288239.900260  225779 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1754288239.900264  225779 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_encoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
Configuration loaded:
  Image size: 512x1024x3
  Batch size: 10
  Epochs: 200
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 297

Creating model...
============================================================
Image JSCC Model Summary
============================================================
Input shape: (512, 1024, 3)
Compression ratio: 1
Quantization bits: 8
Channel type: AWGN
SNR: 10 dB
Compressed features: 8192
Transmitted bits: 65536
Actual compression ratio: 192.00
============================================================
Encoder parameters: 1,919,104
Decoder parameters: 204,130,563
Total trainable parameters: 206,049,667
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_1_20250804_141725/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250804_141725/

Epoch 1/200
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train

Training:   0%|          | 0/297 [00:00<?, ?it/s]
Training:   0%|          | 0/297 [00:59<?, ?it/s, loss=0.1499]
Training:   0%|          | 1/297 [00:59<4:53:35, 59.51s/it, loss=0.1499]
Training:   0%|          | 1/297 [01:18<4:53:35, 59.51s/it, loss=0.1362]
Training:   1%|          | 2/297 [01:18<2:56:10, 35.83s/it, loss=0.1362]
Training:   1%|          | 2/297 [01:37<2:56:10, 35.83s/it, loss=0.1343]
Training:   1%|          | 3/297 [01:37<2:17:33, 28.07s/it, loss=0.1343]
Training:   1%|          | 3/297 [01:56<2:17:33, 28.07s/it, loss=0.1304]
Training:   1%|▏         | 4/297 [01:56<1:59:18, 24.43s/it, loss=0.1304]
Training:   1%|▏         | 4/297 [02:16<1:59:18, 24.43s/it, loss=0.1196]
Training:   2%|▏         | 5/297 [02:16<1:50:30, 22.71s/it, loss=0.1196]
Training:   2%|▏         | 5/297 [02:34<1:50:30, 22.71s/it, loss=0.1254]
Training:   2%|▏         | 6/297 [02:34<1:43:20, 21.31s/it, loss=0.1254]
Training:   2%|▏         | 6/297 [02:53<1:43:20, 21.31s/it, loss=0.1197]
Training:   2%|▏         | 7/297 [02:53<1:38:39, 20.41s/it, loss=0.1197]
Training:   2%|▏         | 7/297 [03:12<1:38:39, 20.41s/it, loss=0.1231]
Training:   3%|▎         | 8/297 [03:12<1:35:45, 19.88s/it, loss=0.1231]
Training:   3%|▎         | 8/297 [03:30<1:35:45, 19.88s/it, loss=0.1147]
Training:   3%|▎         | 9/297 [03:30<1:32:45, 19.32s/it, loss=0.1147]
Training:   3%|▎         | 9/297 [03:48<1:32:45, 19.32s/it, loss=0.1137]
Training:   3%|▎         | 10/297 [03:48<1:31:17, 19.09s/it, loss=0.1137]
Training:   3%|▎         | 10/297 [04:07<1:31:17, 19.09s/it, loss=0.1122]
Training:   4%|▎         | 11/297 [04:07<1:30:11, 18.92s/it, loss=0.1122]
Training:   4%|▎         | 11/297 [04:26<1:30:11, 18.92s/it, loss=0.1113]
Training:   4%|▍         | 12/297 [04:26<1:29:44, 18.89s/it, loss=0.1113]
Training:   4%|▍         | 12/297 [04:44<1:29:44, 18.89s/it, loss=0.1123]
Training:   4%|▍         | 13/297 [04:44<1:28:57, 18.79s/it, loss=0.1123]
Training:   4%|▍         | 13/297 [05:03<1:28:57, 18.79s/it, loss=0.1049]
Training:   5%|▍         | 14/297 [05:03<1:28:27, 18.75s/it, loss=0.1049]
Training:   5%|▍         | 14/297 [05:21<1:28:27, 18.75s/it, loss=0.1073]
Training:   5%|▌         | 15/297 [05:21<1:27:59, 18.72s/it, loss=0.1073]
Training:   5%|▌         | 15/297 [05:40<1:27:59, 18.72s/it, loss=0.1085]
Training:   5%|▌         | 16/297 [05:40<1:27:49, 18.75s/it, loss=0.1085]
Training:   5%|▌         | 16/297 [06:00<1:27:49, 18.75s/it, loss=0.1060]
Training:   6%|▌         | 17/297 [06:00<1:28:21, 18.93s/it, loss=0.1060]
Training:   6%|▌         | 17/297 [06:19<1:28:21, 18.93s/it, loss=0.1059]
Training:   6%|▌         | 18/297 [06:19<1:28:02, 18.93s/it, loss=0.1059]
Training:   6%|▌         | 18/297 [06:38<1:28:02, 18.93s/it, loss=0.1057]
Training:   6%|▋         | 19/297 [06:38<1:28:03, 19.01s/it, loss=0.1057]
Training:   6%|▋         | 19/297 [06:57<1:28:03, 19.01s/it, loss=0.0982]
Training:   7%|▋         | 20/297 [06:57<1:27:34, 18.97s/it, loss=0.0982]
Training:   7%|▋         | 20/297 [07:16<1:27:34, 18.97s/it, loss=0.1026]
Training:   7%|▋         | 21/297 [07:16<1:27:15, 18.97s/it, loss=0.1026]
Training:   7%|▋         | 21/297 [07:34<1:27:15, 18.97s/it, loss=0.0946]
Training:   7%|▋         | 22/297 [07:34<1:26:40, 18.91s/it, loss=0.0946]
Training:   7%|▋         | 22/297 [07:53<1:26:40, 18.91s/it, loss=0.1011]
Training:   8%|▊         | 23/297 [07:53<1:26:26, 18.93s/it, loss=0.1011]
Training:   8%|▊         | 23/297 [08:12<1:26:26, 18.93s/it, loss=0.0989]
Training:   8%|▊         | 24/297 [08:12<1:25:57, 18.89s/it, loss=0.0989]
Training:   8%|▊         | 24/297 [08:31<1:25:57, 18.89s/it, loss=0.0962]
Training:   8%|▊         | 25/297 [08:31<1:25:35, 18.88s/it, loss=0.0962]
Training:   8%|▊         | 25/297 [08:50<1:25:35, 18.88s/it, loss=0.0941]
Training:   9%|▉         | 26/297 [08:50<1:25:19, 18.89s/it, loss=0.0941]
Training:   9%|▉         | 26/297 [09:09<1:25:19, 18.89s/it, loss=0.0938]
Training:   9%|▉         | 27/297 [09:09<1:24:49, 18.85s/it, loss=0.0938]
Training:   9%|▉         | 27/297 [09:27<1:24:49, 18.85s/it, loss=0.1057]
Training:   9%|▉         | 28/297 [09:27<1:24:15, 18.79s/it, loss=0.1057]
Training:   9%|▉         | 28/297 [09:46<1:24:15, 18.79s/it, loss=0.0901]
Training:  10%|▉         | 29/297 [09:46<1:23:59, 18.80s/it, loss=0.0901]
Training:  10%|▉         | 29/297 [10:05<1:23:59, 18.80s/it, loss=0.0788]
Training:  10%|█         | 30/297 [10:05<1:23:36, 18.79s/it, loss=0.0788]
Training:  10%|█         | 30/297 [10:24<1:23:36, 18.79s/it, loss=0.0937]
Training:  10%|█         | 31/297 [10:24<1:23:12, 18.77s/it, loss=0.0937]
Training:  10%|█         | 31/297 [10:43<1:23:12, 18.77s/it, loss=0.0874]
Training:  11%|█         | 32/297 [10:43<1:23:19, 18.86s/it, loss=0.0874]
Training:  11%|█         | 32/297 [11:01<1:23:19, 18.86s/it, loss=0.0890]
Training:  11%|█         | 33/297 [11:01<1:22:46, 18.81s/it, loss=0.0890]
Training:  11%|█         | 33/297 [11:20<1:22:46, 18.81s/it, loss=0.0875]
Training:  11%|█▏        | 34/297 [11:20<1:22:06, 18.73s/it, loss=0.0875]
Training:  11%|█▏        | 34/297 [11:38<1:22:06, 18.73s/it, loss=0.0831]
Training:  12%|█▏        | 35/297 [11:38<1:21:29, 18.66s/it, loss=0.0831]
Training:  12%|█▏        | 35/297 [11:57<1:21:29, 18.66s/it, loss=0.0940]
Training:  12%|█▏        | 36/297 [11:57<1:20:48, 18.58s/it, loss=0.0940]
Training:  12%|█▏        | 36/297 [12:15<1:20:48, 18.58s/it, loss=0.0811]
Training:  12%|█▏        | 37/297 [12:15<1:20:37, 18.61s/it, loss=0.0811]
Training:  12%|█▏        | 37/297 [12:34<1:20:37, 18.61s/it, loss=0.0857]
Training:  13%|█▎        | 38/297 [12:34<1:20:21, 18.62s/it, loss=0.0857]
Training:  13%|█▎        | 38/297 [12:53<1:20:21, 18.62s/it, loss=0.0777]
Training:  13%|█▎        | 39/297 [12:53<1:20:12, 18.65s/it, loss=0.0777]
Training:  13%|█▎        | 39/297 [13:12<1:20:12, 18.65s/it, loss=0.0857]
Training:  13%|█▎        | 40/297 [13:12<1:20:07, 18.71s/it, loss=0.0857]
Training:  13%|█▎        | 40/297 [13:31<1:20:07, 18.71s/it, loss=0.0827]
Training:  14%|█▍        | 41/297 [13:31<1:19:59, 18.75s/it, loss=0.0827]
Training:  14%|█▍        | 41/297 [13:50<1:19:59, 18.75s/it, loss=0.0786]
Training:  14%|█▍        | 42/297 [13:50<1:20:03, 18.84s/it, loss=0.0786]
Training:  14%|█▍        | 42/297 [14:09<1:20:03, 18.84s/it, loss=0.0744]
Training:  14%|█▍        | 43/297 [14:09<1:20:10, 18.94s/it, loss=0.0744]
Training:  14%|█▍        | 43/297 [14:28<1:20:10, 18.94s/it, loss=0.0817]
Training:  15%|█▍        | 44/297 [14:28<1:20:00, 18.98s/it, loss=0.0817]
Training:  15%|█▍        | 44/297 [14:47<1:20:00, 18.98s/it, loss=0.0773]
Training:  15%|█▌        | 45/297 [14:47<1:19:43, 18.98s/it, loss=0.0773]
Training:  15%|█▌        | 45/297 [15:06<1:19:43, 18.98s/it, loss=0.0833]
Training:  15%|█▌        | 46/297 [15:06<1:19:21, 18.97s/it, loss=0.0833]
Training:  15%|█▌        | 46/297 [15:25<1:19:21, 18.97s/it, loss=0.0757]
Training:  16%|█▌        | 47/297 [15:25<1:18:58, 18.95s/it, loss=0.0757]
Training:  16%|█▌        | 47/297 [15:44<1:18:58, 18.95s/it, loss=0.0789]
Training:  16%|█▌        | 48/297 [15:44<1:19:03, 19.05s/it, loss=0.0789]
Training:  16%|█▌        | 48/297 [16:03<1:19:03, 19.05s/it, loss=0.0780]
Training:  16%|█▋        | 49/297 [16:03<1:19:01, 19.12s/it, loss=0.0780]
Training:  16%|█▋        | 49/297 [16:22<1:19:01, 19.12s/it, loss=0.0717]
Training:  17%|█▋        | 50/297 [16:22<1:18:39, 19.11s/it, loss=0.0717]
Training:  17%|█▋        | 50/297 [16:42<1:18:39, 19.11s/it, loss=0.0745]
Training:  17%|█▋        | 51/297 [16:42<1:18:36, 19.17s/it, loss=0.0745]
Training:  17%|█▋        | 51/297 [17:01<1:18:36, 19.17s/it, loss=0.0735]
Training:  18%|█▊        | 52/297 [17:01<1:18:42, 19.28s/it, loss=0.0735]
Training:  18%|█▊        | 52/297 [17:21<1:18:42, 19.28s/it, loss=0.0654]
Training:  18%|█▊        | 53/297 [17:21<1:18:45, 19.37s/it, loss=0.0654]
Training:  18%|█▊        | 53/297 [17:40<1:18:45, 19.37s/it, loss=0.0801]
Training:  18%|█▊        | 54/297 [17:40<1:17:57, 19.25s/it, loss=0.0801]
Training:  18%|█▊        | 54/297 [17:59<1:17:57, 19.25s/it, loss=0.0722]
Training:  19%|█▊        | 55/297 [17:59<1:17:14, 19.15s/it, loss=0.0722]
Training:  19%|█▊        | 55/297 [18:18<1:17:14, 19.15s/it, loss=0.0693]
Training:  19%|█▉        | 56/297 [18:18<1:16:40, 19.09s/it, loss=0.0693]
Training:  19%|█▉        | 56/297 [18:36<1:16:40, 19.09s/it, loss=0.0711]
Training:  19%|█▉        | 57/297 [18:36<1:15:59, 19.00s/it, loss=0.0711]
Training:  19%|█▉        | 57/297 [18:55<1:15:59, 19.00s/it, loss=0.0736]
Training:  20%|█▉        | 58/297 [18:55<1:15:30, 18.96s/it, loss=0.0736]
Training:  20%|█▉        | 58/297 [19:14<1:15:30, 18.96s/it, loss=0.0693]
Training:  20%|█▉        | 59/297 [19:14<1:15:33, 19.05s/it, loss=0.0693]
Training:  20%|█▉        | 59/297 [19:33<1:15:33, 19.05s/it, loss=0.0741]
Training:  20%|██        | 60/297 [19:33<1:14:58, 18.98s/it, loss=0.0741]
Training:  20%|██        | 60/297 [19:52<1:14:58, 18.98s/it, loss=0.0666]
Training:  21%|██        | 61/297 [19:52<1:14:35, 18.96s/it, loss=0.0666]
Training:  21%|██        | 61/297 [20:12<1:14:35, 18.96s/it, loss=0.0698]
Training:  21%|██        | 62/297 [20:12<1:14:53, 19.12s/it, loss=0.0698]
Training:  21%|██        | 62/297 [20:31<1:14:53, 19.12s/it, loss=0.0660]
Training:  21%|██        | 63/297 [20:31<1:14:43, 19.16s/it, loss=0.0660]
Training:  21%|██        | 63/297 [20:50<1:14:43, 19.16s/it, loss=0.0707]
Training:  22%|██▏       | 64/297 [20:50<1:13:42, 18.98s/it, loss=0.0707]
Training:  22%|██▏       | 64/297 [21:08<1:13:42, 18.98s/it, loss=0.0717]
Training:  22%|██▏       | 65/297 [21:08<1:13:08, 18.92s/it, loss=0.0717]
Training:  22%|██▏       | 65/297 [21:27<1:13:08, 18.92s/it, loss=0.0692]
Training:  22%|██▏       | 66/297 [21:27<1:12:23, 18.80s/it, loss=0.0692]
Training:  22%|██▏       | 66/297 [21:46<1:12:23, 18.80s/it, loss=0.0756]
Training:  23%|██▎       | 67/297 [21:46<1:12:04, 18.80s/it, loss=0.0756]
Training:  23%|██▎       | 67/297 [22:04<1:12:04, 18.80s/it, loss=0.0590]
Training:  23%|██▎       | 68/297 [22:04<1:11:43, 18.79s/it, loss=0.0590]
Training:  23%|██▎       | 68/297 [22:23<1:11:43, 18.79s/it, loss=0.0635]
Training:  23%|██▎       | 69/297 [22:23<1:11:38, 18.85s/it, loss=0.0635]
Training:  23%|██▎       | 69/297 [22:42<1:11:38, 18.85s/it, loss=0.0749]
Training:  24%|██▎       | 70/297 [22:42<1:11:36, 18.93s/it, loss=0.0749]
Training:  24%|██▎       | 70/297 [23:01<1:11:36, 18.93s/it, loss=0.0725]
Training:  24%|██▍       | 71/297 [23:01<1:11:06, 18.88s/it, loss=0.0725]
Training:  24%|██▍       | 71/297 [23:20<1:11:06, 18.88s/it, loss=0.0695]
Training:  24%|██▍       | 72/297 [23:20<1:10:46, 18.87s/it, loss=0.0695]
Training:  24%|██▍       | 72/297 [23:39<1:10:46, 18.87s/it, loss=0.0643]
Training:  25%|██▍       | 73/297 [23:39<1:10:48, 18.97s/it, loss=0.0643]
Training:  25%|██▍       | 73/297 [23:58<1:10:48, 18.97s/it, loss=0.0636]
Training:  25%|██▍       | 74/297 [23:58<1:10:17, 18.91s/it, loss=0.0636]
Training:  25%|██▍       | 74/297 [24:17<1:10:17, 18.91s/it, loss=0.0603]
Training:  25%|██▌       | 75/297 [24:17<1:09:42, 18.84s/it, loss=0.0603]
Training:  25%|██▌       | 75/297 [24:35<1:09:42, 18.84s/it, loss=0.0600]
Training:  26%|██▌       | 76/297 [24:35<1:09:15, 18.80s/it, loss=0.0600]
Training:  26%|██▌       | 76/297 [24:55<1:09:15, 18.80s/it, loss=0.0634]
Training:  26%|██▌       | 77/297 [24:55<1:09:21, 18.92s/it, loss=0.0634]
Training:  26%|██▌       | 77/297 [25:14<1:09:21, 18.92s/it, loss=0.0719]
Training:  26%|██▋       | 78/297 [25:14<1:09:11, 18.96s/it, loss=0.0719]
Training:  26%|██▋       | 78/297 [25:33<1:09:11, 18.96s/it, loss=0.0604]
Training:  27%|██▋       | 79/297 [25:33<1:09:18, 19.08s/it, loss=0.0604]
Training:  27%|██▋       | 79/297 [25:52<1:09:18, 19.08s/it, loss=0.0634]
Training:  27%|██▋       | 80/297 [25:52<1:08:35, 18.96s/it, loss=0.0634]
Training:  27%|██▋       | 80/297 [26:10<1:08:35, 18.96s/it, loss=0.0671]
Training:  27%|██▋       | 81/297 [26:10<1:07:47, 18.83s/it, loss=0.0671]
Training:  27%|██▋       | 81/297 [26:29<1:07:47, 18.83s/it, loss=0.0594]
Training:  28%|██▊       | 82/297 [26:29<1:07:19, 18.79s/it, loss=0.0594]
Training:  28%|██▊       | 82/297 [26:48<1:07:19, 18.79s/it, loss=0.0616]
Training:  28%|██▊       | 83/297 [26:48<1:07:04, 18.81s/it, loss=0.0616]
Training:  28%|██▊       | 83/297 [27:07<1:07:04, 18.81s/it, loss=0.0656]
Training:  28%|██▊       | 84/297 [27:07<1:06:47, 18.82s/it, loss=0.0656]
Training:  28%|██▊       | 84/297 [27:25<1:06:47, 18.82s/it, loss=0.0765]
Training:  29%|██▊       | 85/297 [27:25<1:06:28, 18.82s/it, loss=0.0765]
Training:  29%|██▊       | 85/297 [27:44<1:06:28, 18.82s/it, loss=0.0653]
Training:  29%|██▉       | 86/297 [27:44<1:05:39, 18.67s/it, loss=0.0653]
Training:  29%|██▉       | 86/297 [28:03<1:05:39, 18.67s/it, loss=0.0639]
Training:  29%|██▉       | 87/297 [28:03<1:05:35, 18.74s/it, loss=0.0639]
Training:  29%|██▉       | 87/297 [28:22<1:05:35, 18.74s/it, loss=0.0604]
Training:  30%|██▉       | 88/297 [28:22<1:05:37, 18.84s/it, loss=0.0604]
Training:  30%|██▉       | 88/297 [28:41<1:05:37, 18.84s/it, loss=0.0575]
Training:  30%|██▉       | 89/297 [28:41<1:05:21, 18.85s/it, loss=0.0575]
Training:  30%|██▉       | 89/297 [28:59<1:05:21, 18.85s/it, loss=0.0603]
Training:  30%|███       | 90/297 [28:59<1:04:47, 18.78s/it, loss=0.0603]
Training:  30%|███       | 90/297 [29:18<1:04:47, 18.78s/it, loss=0.0589]
Training:  31%|███       | 91/297 [29:18<1:04:35, 18.82s/it, loss=0.0589]
Training:  31%|███       | 91/297 [29:37<1:04:35, 18.82s/it, loss=0.0610]
Training:  31%|███       | 92/297 [29:37<1:04:24, 18.85s/it, loss=0.0610]
Training:  31%|███       | 92/297 [29:56<1:04:24, 18.85s/it, loss=0.0639]
Training:  31%|███▏      | 93/297 [29:56<1:04:04, 18.84s/it, loss=0.0639]
Training:  31%|███▏      | 93/297 [30:15<1:04:04, 18.84s/it, loss=0.0575]
Training:  32%|███▏      | 94/297 [30:15<1:04:04, 18.94s/it, loss=0.0575]
Training:  32%|███▏      | 94/297 [30:34<1:04:04, 18.94s/it, loss=0.0640]
Training:  32%|███▏      | 95/297 [30:34<1:04:01, 19.02s/it, loss=0.0640]
Training:  32%|███▏      | 95/297 [30:53<1:04:01, 19.02s/it, loss=0.0614]
Training:  32%|███▏      | 96/297 [30:53<1:03:37, 18.99s/it, loss=0.0614]
Training:  32%|███▏      | 96/297 [31:12<1:03:37, 18.99s/it, loss=0.0612]
Training:  33%|███▎      | 97/297 [31:12<1:03:30, 19.05s/it, loss=0.0612]
Training:  33%|███▎      | 97/297 [31:32<1:03:30, 19.05s/it, loss=0.0579]
Training:  33%|███▎      | 98/297 [31:32<1:03:29, 19.14s/it, loss=0.0579]
Training:  33%|███▎      | 98/297 [31:51<1:03:29, 19.14s/it, loss=0.0600]
Training:  33%|███▎      | 99/297 [31:51<1:03:03, 19.11s/it, loss=0.0600]
Training:  33%|███▎      | 99/297 [32:10<1:03:03, 19.11s/it, loss=0.0572]
Training:  34%|███▎      | 100/297 [32:10<1:02:32, 19.05s/it, loss=0.0572]
Training:  34%|███▎      | 100/297 [32:28<1:02:32, 19.05s/it, loss=0.0634]
Training:  34%|███▍      | 101/297 [32:28<1:01:49, 18.92s/it, loss=0.0634]
Training:  34%|███▍      | 101/297 [32:47<1:01:49, 18.92s/it, loss=0.0531]
Training:  34%|███▍      | 102/297 [32:47<1:01:29, 18.92s/it, loss=0.0531]
Training:  34%|███▍      | 102/297 [33:06<1:01:29, 18.92s/it, loss=0.0537]
Training:  35%|███▍      | 103/297 [33:06<1:01:11, 18.92s/it, loss=0.0537]
Training:  35%|███▍      | 103/297 [33:25<1:01:11, 18.92s/it, loss=0.0619]
Training:  35%|███▌      | 104/297 [33:25<1:00:49, 18.91s/it, loss=0.0619]
Training:  35%|███▌      | 104/297 [33:44<1:00:49, 18.91s/it, loss=0.0531]
Training:  35%|███▌      | 105/297 [33:44<1:00:31, 18.91s/it, loss=0.0531]
Training:  35%|███▌      | 105/297 [34:03<1:00:31, 18.91s/it, loss=0.0572]
Training:  36%|███▌      | 106/297 [34:03<1:00:32, 19.02s/it, loss=0.0572]
Training:  36%|███▌      | 106/297 [34:22<1:00:32, 19.02s/it, loss=0.0528]
Training:  36%|███▌      | 107/297 [34:22<1:00:08, 18.99s/it, loss=0.0528]
Training:  36%|███▌      | 107/297 [34:41<1:00:08, 18.99s/it, loss=0.0577]
Training:  36%|███▋      | 108/297 [34:41<59:46, 18.98s/it, loss=0.0577]  
Training:  36%|███▋      | 108/297 [35:00<59:46, 18.98s/it, loss=0.0512]
Training:  37%|███▋      | 109/297 [35:00<59:18, 18.93s/it, loss=0.0512]
Training:  37%|███▋      | 109/297 [35:19<59:18, 18.93s/it, loss=0.0572]
Training:  37%|███▋      | 110/297 [35:19<59:02, 18.94s/it, loss=0.0572]
Training:  37%|███▋      | 110/297 [35:38<59:02, 18.94s/it, loss=0.0470]
Training:  37%|███▋      | 111/297 [35:38<59:11, 19.09s/it, loss=0.0470]
Training:  37%|███▋      | 111/297 [35:58<59:11, 19.09s/it, loss=0.0557]
Training:  38%|███▊      | 112/297 [35:58<58:59, 19.13s/it, loss=0.0557]
Training:  38%|███▊      | 112/297 [36:17<58:59, 19.13s/it, loss=0.0581]
Training:  38%|███▊      | 113/297 [36:17<58:39, 19.13s/it, loss=0.0581]
Training:  38%|███▊      | 113/297 [36:36<58:39, 19.13s/it, loss=0.0592]
Training:  38%|███▊      | 114/297 [36:36<58:06, 19.05s/it, loss=0.0592]
Training:  38%|███▊      | 114/297 [36:55<58:06, 19.05s/it, loss=0.0547]
Training:  39%|███▊      | 115/297 [36:55<58:11, 19.18s/it, loss=0.0547]
Training:  39%|███▊      | 115/297 [37:14<58:11, 19.18s/it, loss=0.0558]
Training:  39%|███▉      | 116/297 [37:14<58:02, 19.24s/it, loss=0.0558]
Training:  39%|███▉      | 116/297 [37:34<58:02, 19.24s/it, loss=0.0525]
Training:  39%|███▉      | 117/297 [37:34<57:45, 19.26s/it, loss=0.0525]
Training:  39%|███▉      | 117/297 [37:53<57:45, 19.26s/it, loss=0.0586]
Training:  40%|███▉      | 118/297 [37:53<57:26, 19.25s/it, loss=0.0586]
Training:  40%|███▉      | 118/297 [38:12<57:26, 19.25s/it, loss=0.0532]
Training:  40%|████      | 119/297 [38:12<57:07, 19.25s/it, loss=0.0532]
Training:  40%|████      | 119/297 [38:31<57:07, 19.25s/it, loss=0.0514]
Training:  40%|████      | 120/297 [38:31<56:25, 19.13s/it, loss=0.0514]
Training:  40%|████      | 120/297 [38:50<56:25, 19.13s/it, loss=0.0501]
Training:  41%|████      | 121/297 [38:50<55:56, 19.07s/it, loss=0.0501]
Training:  41%|████      | 121/297 [39:09<55:56, 19.07s/it, loss=0.0477]
Training:  41%|████      | 122/297 [39:09<55:29, 19.02s/it, loss=0.0477]
Training:  41%|████      | 122/297 [39:28<55:29, 19.02s/it, loss=0.0503]
Training:  41%|████▏     | 123/297 [39:28<55:04, 18.99s/it, loss=0.0503]
Training:  41%|████▏     | 123/297 [39:47<55:04, 18.99s/it, loss=0.0489]
Training:  42%|████▏     | 124/297 [39:47<54:41, 18.97s/it, loss=0.0489]
Training:  42%|████▏     | 124/297 [40:06<54:41, 18.97s/it, loss=0.0431]
Training:  42%|████▏     | 125/297 [40:06<54:26, 18.99s/it, loss=0.0431]
Training:  42%|████▏     | 125/297 [40:24<54:26, 18.99s/it, loss=0.0515]
Training:  42%|████▏     | 126/297 [40:24<53:51, 18.90s/it, loss=0.0515]
Training:  42%|████▏     | 126/297 [40:43<53:51, 18.90s/it, loss=0.0452]
Training:  43%|████▎     | 127/297 [40:43<53:30, 18.89s/it, loss=0.0452]
Training:  43%|████▎     | 127/297 [41:02<53:30, 18.89s/it, loss=0.0475]
Training:  43%|████▎     | 128/297 [41:02<53:06, 18.86s/it, loss=0.0475]
Training:  43%|████▎     | 128/297 [41:21<53:06, 18.86s/it, loss=0.0437]
Training:  43%|████▎     | 129/297 [41:21<52:47, 18.85s/it, loss=0.0437]
Training:  43%|████▎     | 129/297 [41:40<52:47, 18.85s/it, loss=0.0426]
Training:  44%|████▍     | 130/297 [41:40<52:21, 18.81s/it, loss=0.0426]
Training:  44%|████▍     | 130/297 [41:58<52:21, 18.81s/it, loss=0.0378]
Training:  44%|████▍     | 131/297 [41:58<52:02, 18.81s/it, loss=0.0378]
Training:  44%|████▍     | 131/297 [42:17<52:02, 18.81s/it, loss=0.0439]
Training:  44%|████▍     | 132/297 [42:17<51:52, 18.86s/it, loss=0.0439]
Training:  44%|████▍     | 132/297 [42:36<51:52, 18.86s/it, loss=0.0512]
Training:  45%|████▍     | 133/297 [42:36<51:30, 18.85s/it, loss=0.0512]
Training:  45%|████▍     | 133/297 [42:56<51:30, 18.85s/it, loss=0.0486]
Training:  45%|████▌     | 134/297 [42:56<51:32, 18.98s/it, loss=0.0486]
Training:  45%|████▌     | 134/297 [43:15<51:32, 18.98s/it, loss=0.0472]
Training:  45%|████▌     | 135/297 [43:15<51:18, 19.00s/it, loss=0.0472]
Training:  45%|████▌     | 135/297 [43:34<51:18, 19.00s/it, loss=0.0445]
Training:  46%|████▌     | 136/297 [43:34<50:57, 18.99s/it, loss=0.0445]
Training:  46%|████▌     | 136/297 [43:53<50:57, 18.99s/it, loss=0.0406]
Training:  46%|████▌     | 137/297 [43:53<50:51, 19.07s/it, loss=0.0406]
Training:  46%|████▌     | 137/297 [44:12<50:51, 19.07s/it, loss=0.0415]
Training:  46%|████▋     | 138/297 [44:12<50:49, 19.18s/it, loss=0.0415]
Training:  46%|████▋     | 138/297 [44:32<50:49, 19.18s/it, loss=0.0406]
Training:  47%|████▋     | 139/297 [44:32<50:38, 19.23s/it, loss=0.0406]
Training:  47%|████▋     | 139/297 [44:51<50:38, 19.23s/it, loss=0.0437]
Training:  47%|████▋     | 140/297 [44:51<50:22, 19.25s/it, loss=0.0437]
Training:  47%|████▋     | 140/297 [45:10<50:22, 19.25s/it, loss=0.0409]
Training:  47%|████▋     | 141/297 [45:10<49:59, 19.23s/it, loss=0.0409]
Training:  47%|████▋     | 141/297 [45:29<49:59, 19.23s/it, loss=0.0387]
Training:  48%|████▊     | 142/297 [45:29<49:44, 19.25s/it, loss=0.0387]
Training:  48%|████▊     | 142/297 [45:49<49:44, 19.25s/it, loss=0.0456]
Training:  48%|████▊     | 143/297 [45:49<49:37, 19.34s/it, loss=0.0456]
Training:  48%|████▊     | 143/297 [46:08<49:37, 19.34s/it, loss=0.0428]
Training:  48%|████▊     | 144/297 [46:08<49:02, 19.23s/it, loss=0.0428]
Training:  48%|████▊     | 144/297 [46:27<49:02, 19.23s/it, loss=0.0483]
Training:  49%|████▉     | 145/297 [46:27<48:37, 19.19s/it, loss=0.0483]
Training:  49%|████▉     | 145/297 [46:46<48:37, 19.19s/it, loss=0.0442]
Training:  49%|████▉     | 146/297 [46:46<48:18, 19.19s/it, loss=0.0442]
Training:  49%|████▉     | 146/297 [47:06<48:18, 19.19s/it, loss=0.0396]
Training:  49%|████▉     | 147/297 [47:06<48:03, 19.23s/it, loss=0.0396]
Training:  49%|████▉     | 147/297 [47:25<48:03, 19.23s/it, loss=0.0460]
Training:  50%|████▉     | 148/297 [47:25<47:34, 19.16s/it, loss=0.0460]
Training:  50%|████▉     | 148/297 [47:44<47:34, 19.16s/it, loss=0.0445]
Training:  50%|█████     | 149/297 [47:44<47:14, 19.15s/it, loss=0.0445]
Training:  50%|█████     | 149/297 [48:03<47:14, 19.15s/it, loss=0.0414]
Training:  51%|█████     | 150/297 [48:03<47:01, 19.20s/it, loss=0.0414]
Training:  51%|█████     | 150/297 [48:22<47:01, 19.20s/it, loss=0.0401]
Training:  51%|█████     | 151/297 [48:22<46:45, 19.21s/it, loss=0.0401]
Training:  51%|█████     | 151/297 [48:41<46:45, 19.21s/it, loss=0.0411]
Training:  51%|█████     | 152/297 [48:41<46:22, 19.19s/it, loss=0.0411]
Training:  51%|█████     | 152/297 [49:00<46:22, 19.19s/it, loss=0.0365]
Training:  52%|█████▏    | 153/297 [49:00<45:50, 19.10s/it, loss=0.0365]
Training:  52%|█████▏    | 153/297 [49:19<45:50, 19.10s/it, loss=0.0424]
Training:  52%|█████▏    | 154/297 [49:19<45:16, 19.00s/it, loss=0.0424]
Training:  52%|█████▏    | 154/297 [49:38<45:16, 19.00s/it, loss=0.0433]
Training:  52%|█████▏    | 155/297 [49:38<44:54, 18.97s/it, loss=0.0433]
Training:  52%|█████▏    | 155/297 [49:57<44:54, 18.97s/it, loss=0.0386]
Training:  53%|█████▎    | 156/297 [49:57<44:33, 18.96s/it, loss=0.0386]
Training:  53%|█████▎    | 156/297 [50:16<44:33, 18.96s/it, loss=0.0369]
Training:  53%|█████▎    | 157/297 [50:16<44:21, 19.01s/it, loss=0.0369]
Training:  53%|█████▎    | 157/297 [50:35<44:21, 19.01s/it, loss=0.0402]
Training:  53%|█████▎    | 158/297 [50:35<43:58, 18.98s/it, loss=0.0402]
Training:  53%|█████▎    | 158/297 [50:54<43:58, 18.98s/it, loss=0.0415]
Training:  54%|█████▎    | 159/297 [50:54<43:26, 18.89s/it, loss=0.0415]
Training:  54%|█████▎    | 159/297 [51:12<43:26, 18.89s/it, loss=0.0465]
Training:  54%|█████▍    | 160/297 [51:12<42:53, 18.79s/it, loss=0.0465]
Training:  54%|█████▍    | 160/297 [51:31<42:53, 18.79s/it, loss=0.0460]
Training:  54%|█████▍    | 161/297 [51:31<42:27, 18.73s/it, loss=0.0460]
Training:  54%|█████▍    | 161/297 [51:49<42:27, 18.73s/it, loss=0.0428]
Training:  55%|█████▍    | 162/297 [51:49<41:52, 18.61s/it, loss=0.0428]
Training:  55%|█████▍    | 162/297 [52:08<41:52, 18.61s/it, loss=0.0428]
Training:  55%|█████▍    | 163/297 [52:08<41:28, 18.57s/it, loss=0.0428]
Training:  55%|█████▍    | 163/297 [52:26<41:28, 18.57s/it, loss=0.0384]
Training:  55%|█████▌    | 164/297 [52:26<41:04, 18.53s/it, loss=0.0384]
Training:  55%|█████▌    | 164/297 [52:45<41:04, 18.53s/it, loss=0.0442]
Training:  56%|█████▌    | 165/297 [52:45<41:06, 18.68s/it, loss=0.0442]
Training:  56%|█████▌    | 165/297 [53:04<41:06, 18.68s/it, loss=0.0420]
Training:  56%|█████▌    | 166/297 [53:04<40:46, 18.68s/it, loss=0.0420]
Training:  56%|█████▌    | 166/297 [53:23<40:46, 18.68s/it, loss=0.0402]
Training:  56%|█████▌    | 167/297 [53:23<40:40, 18.77s/it, loss=0.0402]
Training:  56%|█████▌    | 167/297 [53:41<40:40, 18.77s/it, loss=0.0361]
Training:  57%|█████▋    | 168/297 [53:41<40:15, 18.72s/it, loss=0.0361]
Training:  57%|█████▋    | 168/297 [54:00<40:15, 18.72s/it, loss=0.0403]
Training:  57%|█████▋    | 169/297 [54:00<39:44, 18.63s/it, loss=0.0403]
Training:  57%|█████▋    | 169/297 [54:18<39:44, 18.63s/it, loss=0.0391]
Training:  57%|█████▋    | 170/297 [54:18<39:28, 18.65s/it, loss=0.0391]
Training:  57%|█████▋    | 170/297 [54:37<39:28, 18.65s/it, loss=0.0415]
Training:  58%|█████▊    | 171/297 [54:37<39:15, 18.70s/it, loss=0.0415]
Training:  58%|█████▊    | 171/297 [54:56<39:15, 18.70s/it, loss=0.0362]
Training:  58%|█████▊    | 172/297 [54:56<38:45, 18.61s/it, loss=0.0362]
Training:  58%|█████▊    | 172/297 [55:14<38:45, 18.61s/it, loss=0.0420]
Training:  58%|█████▊    | 173/297 [55:14<38:30, 18.63s/it, loss=0.0420]
Training:  58%|█████▊    | 173/297 [55:33<38:30, 18.63s/it, loss=0.0419]
Training:  59%|█████▊    | 174/297 [55:33<38:06, 18.59s/it, loss=0.0419]
Training:  59%|█████▊    | 174/297 [55:51<38:06, 18.59s/it, loss=0.0361]
Training:  59%|█████▉    | 175/297 [55:51<37:42, 18.54s/it, loss=0.0361]
Training:  59%|█████▉    | 175/297 [56:10<37:42, 18.54s/it, loss=0.0463]
Training:  59%|█████▉    | 176/297 [56:10<37:19, 18.50s/it, loss=0.0463]
Training:  59%|█████▉    | 176/297 [56:28<37:19, 18.50s/it, loss=0.0413]
Training:  60%|█████▉    | 177/297 [56:28<37:04, 18.54s/it, loss=0.0413]
Training:  60%|█████▉    | 177/297 [56:47<37:04, 18.54s/it, loss=0.0415]
Training:  60%|█████▉    | 178/297 [56:47<36:44, 18.53s/it, loss=0.0415]
Training:  60%|█████▉    | 178/297 [57:05<36:44, 18.53s/it, loss=0.0409]
Training:  60%|██████    | 179/297 [57:05<36:25, 18.52s/it, loss=0.0409]
Training:  60%|██████    | 179/297 [57:24<36:25, 18.52s/it, loss=0.0470]
Training:  61%|██████    | 180/297 [57:24<36:13, 18.58s/it, loss=0.0470]
Training:  61%|██████    | 180/297 [57:43<36:13, 18.58s/it, loss=0.0397]
Training:  61%|██████    | 181/297 [57:43<35:56, 18.59s/it, loss=0.0397]
Training:  61%|██████    | 181/297 [58:01<35:56, 18.59s/it, loss=0.0428]
Training:  61%|██████▏   | 182/297 [58:01<35:49, 18.69s/it, loss=0.0428]
Training:  61%|██████▏   | 182/297 [58:20<35:49, 18.69s/it, loss=0.0396]
Training:  62%|██████▏   | 183/297 [58:20<35:33, 18.71s/it, loss=0.0396]
Training:  62%|██████▏   | 183/297 [58:39<35:33, 18.71s/it, loss=0.0415]
Training:  62%|██████▏   | 184/297 [58:39<35:09, 18.67s/it, loss=0.0415]
Training:  62%|██████▏   | 184/297 [58:57<35:09, 18.67s/it, loss=0.0419]
Training:  62%|██████▏   | 185/297 [58:57<34:50, 18.66s/it, loss=0.0419]
Training:  62%|██████▏   | 185/297 [59:16<34:50, 18.66s/it, loss=0.0397]
Training:  63%|██████▎   | 186/297 [59:16<34:25, 18.60s/it, loss=0.0397]
Training:  63%|██████▎   | 186/297 [59:34<34:25, 18.60s/it, loss=0.0450]
Training:  63%|██████▎   | 187/297 [59:34<33:58, 18.54s/it, loss=0.0450]
Training:  63%|██████▎   | 187/297 [59:53<33:58, 18.54s/it, loss=0.0464]
Training:  63%|██████▎   | 188/297 [59:53<33:37, 18.51s/it, loss=0.0464]
Training:  63%|██████▎   | 188/297 [1:00:11<33:37, 18.51s/it, loss=0.0381]
Training:  64%|██████▎   | 189/297 [1:00:11<33:18, 18.51s/it, loss=0.0381]
Training:  64%|██████▎   | 189/297 [1:00:30<33:18, 18.51s/it, loss=0.0333]
Training:  64%|██████▍   | 190/297 [1:00:30<32:55, 18.46s/it, loss=0.0333]
Training:  64%|██████▍   | 190/297 [1:00:48<32:55, 18.46s/it, loss=0.0410]
Training:  64%|██████▍   | 191/297 [1:00:48<32:39, 18.49s/it, loss=0.0410]
Training:  64%|██████▍   | 191/297 [1:01:07<32:39, 18.49s/it, loss=0.0416]
Training:  65%|██████▍   | 192/297 [1:01:07<32:21, 18.49s/it, loss=0.0416]
Training:  65%|██████▍   | 192/297 [1:01:25<32:21, 18.49s/it, loss=0.0448]
Training:  65%|██████▍   | 193/297 [1:01:25<32:03, 18.49s/it, loss=0.0448]
Training:  65%|██████▍   | 193/297 [1:01:44<32:03, 18.49s/it, loss=0.0382]
Training:  65%|██████▌   | 194/297 [1:01:44<31:49, 18.54s/it, loss=0.0382]
Training:  65%|██████▌   | 194/297 [1:02:02<31:49, 18.54s/it, loss=0.0430]
Training:  66%|██████▌   | 195/297 [1:02:02<31:28, 18.52s/it, loss=0.0430]
Training:  66%|██████▌   | 195/297 [1:02:21<31:28, 18.52s/it, loss=0.0385]
Training:  66%|██████▌   | 196/297 [1:02:21<31:05, 18.47s/it, loss=0.0385]
Training:  66%|██████▌   | 196/297 [1:02:39<31:05, 18.47s/it, loss=0.0412]
Training:  66%|██████▋   | 197/297 [1:02:39<30:51, 18.52s/it, loss=0.0412]
Training:  66%|██████▋   | 197/297 [1:02:57<30:51, 18.52s/it, loss=0.0423]
Training:  67%|██████▋   | 198/297 [1:02:57<30:22, 18.41s/it, loss=0.0423]
Training:  67%|██████▋   | 198/297 [1:03:16<30:22, 18.41s/it, loss=0.0404]
Training:  67%|██████▋   | 199/297 [1:03:16<30:05, 18.42s/it, loss=0.0404]
Training:  67%|██████▋   | 199/297 [1:03:35<30:05, 18.42s/it, loss=0.0334]
Training:  67%|██████▋   | 200/297 [1:03:35<29:54, 18.50s/it, loss=0.0334]
Training:  67%|██████▋   | 200/297 [1:03:53<29:54, 18.50s/it, loss=0.0437]
Training:  68%|██████▊   | 201/297 [1:03:53<29:35, 18.50s/it, loss=0.0437]
Training:  68%|██████▊   | 201/297 [1:04:12<29:35, 18.50s/it, loss=0.0419]
Training:  68%|██████▊   | 202/297 [1:04:12<29:21, 18.54s/it, loss=0.0419]
Training:  68%|██████▊   | 202/297 [1:04:30<29:21, 18.54s/it, loss=0.0407]
Training:  68%|██████▊   | 203/297 [1:04:30<28:59, 18.51s/it, loss=0.0407]
Training:  68%|██████▊   | 203/297 [1:04:49<28:59, 18.51s/it, loss=0.0354]
Training:  69%|██████▊   | 204/297 [1:04:49<28:37, 18.47s/it, loss=0.0354]
Training:  69%|██████▊   | 204/297 [1:05:07<28:37, 18.47s/it, loss=0.0442]
Training:  69%|██████▉   | 205/297 [1:05:07<28:18, 18.46s/it, loss=0.0442]
Training:  69%|██████▉   | 205/297 [1:05:26<28:18, 18.46s/it, loss=0.0417]
Training:  69%|██████▉   | 206/297 [1:05:26<28:04, 18.51s/it, loss=0.0417]
Training:  69%|██████▉   | 206/297 [1:05:44<28:04, 18.51s/it, loss=0.0397]
Training:  70%|██████▉   | 207/297 [1:05:44<27:54, 18.61s/it, loss=0.0397]
Training:  70%|██████▉   | 207/297 [1:06:03<27:54, 18.61s/it, loss=0.0452]
Training:  70%|███████   | 208/297 [1:06:03<27:35, 18.60s/it, loss=0.0452]
Training:  70%|███████   | 208/297 [1:06:22<27:35, 18.60s/it, loss=0.0436]
Training:  70%|███████   | 209/297 [1:06:22<27:21, 18.66s/it, loss=0.0436]
Training:  70%|███████   | 209/297 [1:06:40<27:21, 18.66s/it, loss=0.0364]
Training:  71%|███████   | 210/297 [1:06:40<26:57, 18.59s/it, loss=0.0364]
Training:  71%|███████   | 210/297 [1:06:59<26:57, 18.59s/it, loss=0.0362]
Training:  71%|███████   | 211/297 [1:06:59<26:38, 18.59s/it, loss=0.0362]
Training:  71%|███████   | 211/297 [1:07:18<26:38, 18.59s/it, loss=0.0399]
Training:  71%|███████▏  | 212/297 [1:07:18<26:25, 18.65s/it, loss=0.0399]
Training:  71%|███████▏  | 212/297 [1:07:36<26:25, 18.65s/it, loss=0.0371]
Training:  72%|███████▏  | 213/297 [1:07:36<26:07, 18.66s/it, loss=0.0371]
Training:  72%|███████▏  | 213/297 [1:07:55<26:07, 18.66s/it, loss=0.0376]
Training:  72%|███████▏  | 214/297 [1:07:55<25:47, 18.65s/it, loss=0.0376]
Training:  72%|███████▏  | 214/297 [1:08:14<25:47, 18.65s/it, loss=0.0384]
Training:  72%|███████▏  | 215/297 [1:08:14<25:30, 18.66s/it, loss=0.0384]
Training:  72%|███████▏  | 215/297 [1:08:32<25:30, 18.66s/it, loss=0.0407]
Training:  73%|███████▎  | 216/297 [1:08:32<25:00, 18.53s/it, loss=0.0407]
Training:  73%|███████▎  | 216/297 [1:08:50<25:00, 18.53s/it, loss=0.0353]
Training:  73%|███████▎  | 217/297 [1:08:50<24:34, 18.44s/it, loss=0.0353]
Training:  73%|███████▎  | 217/297 [1:09:08<24:34, 18.44s/it, loss=0.0397]
Training:  73%|███████▎  | 218/297 [1:09:08<24:09, 18.35s/it, loss=0.0397]
Training:  73%|███████▎  | 218/297 [1:09:26<24:09, 18.35s/it, loss=0.0360]
Training:  74%|███████▎  | 219/297 [1:09:26<23:45, 18.28s/it, loss=0.0360]
Training:  74%|███████▎  | 219/297 [1:09:45<23:45, 18.28s/it, loss=0.0366]
Training:  74%|███████▍  | 220/297 [1:09:45<23:31, 18.33s/it, loss=0.0366]
Training:  74%|███████▍  | 220/297 [1:10:03<23:31, 18.33s/it, loss=0.0405]
Training:  74%|███████▍  | 221/297 [1:10:03<23:13, 18.34s/it, loss=0.0405]
Training:  74%|███████▍  | 221/297 [1:10:22<23:13, 18.34s/it, loss=0.0411]
Training:  75%|███████▍  | 222/297 [1:10:22<22:59, 18.39s/it, loss=0.0411]
Training:  75%|███████▍  | 222/297 [1:10:41<22:59, 18.39s/it, loss=0.0464]
Training:  75%|███████▌  | 223/297 [1:10:41<22:52, 18.55s/it, loss=0.0464]
Training:  75%|███████▌  | 223/297 [1:10:59<22:52, 18.55s/it, loss=0.0378]
Training:  75%|███████▌  | 224/297 [1:10:59<22:29, 18.48s/it, loss=0.0378]
Training:  75%|███████▌  | 224/297 [1:11:18<22:29, 18.48s/it, loss=0.0417]
Training:  76%|███████▌  | 225/297 [1:11:18<22:18, 18.58s/it, loss=0.0417]
Training:  76%|███████▌  | 225/297 [1:11:36<22:18, 18.58s/it, loss=0.0327]
Training:  76%|███████▌  | 226/297 [1:11:36<21:58, 18.58s/it, loss=0.0327]
Training:  76%|███████▌  | 226/297 [1:11:55<21:58, 18.58s/it, loss=0.0381]
Training:  76%|███████▋  | 227/297 [1:11:55<21:35, 18.51s/it, loss=0.0381]
Training:  76%|███████▋  | 227/297 [1:12:13<21:35, 18.51s/it, loss=0.0427]
Training:  77%|███████▋  | 228/297 [1:12:13<21:10, 18.41s/it, loss=0.0427]
Training:  77%|███████▋  | 228/297 [1:12:31<21:10, 18.41s/it, loss=0.0409]
Training:  77%|███████▋  | 229/297 [1:12:31<20:45, 18.31s/it, loss=0.0409]
Training:  77%|███████▋  | 229/297 [1:12:49<20:45, 18.31s/it, loss=0.0416]
Training:  77%|███████▋  | 230/297 [1:12:49<20:26, 18.31s/it, loss=0.0416]
Training:  77%|███████▋  | 230/297 [1:13:07<20:26, 18.31s/it, loss=0.0396]
Training:  78%|███████▊  | 231/297 [1:13:07<20:08, 18.32s/it, loss=0.0396]
Training:  78%|███████▊  | 231/297 [1:13:26<20:08, 18.32s/it, loss=0.0405]
Training:  78%|███████▊  | 232/297 [1:13:26<19:49, 18.30s/it, loss=0.0405]
Training:  78%|███████▊  | 232/297 [1:13:44<19:49, 18.30s/it, loss=0.0413]
Training:  78%|███████▊  | 233/297 [1:13:44<19:26, 18.23s/it, loss=0.0413]
Training:  78%|███████▊  | 233/297 [1:14:02<19:26, 18.23s/it, loss=0.0334]
Training:  79%|███████▉  | 234/297 [1:14:02<19:06, 18.20s/it, loss=0.0334]
Training:  79%|███████▉  | 234/297 [1:14:20<19:06, 18.20s/it, loss=0.0484]
Training:  79%|███████▉  | 235/297 [1:14:20<18:53, 18.28s/it, loss=0.0484]
Training:  79%|███████▉  | 235/297 [1:14:39<18:53, 18.28s/it, loss=0.0385]
Training:  79%|███████▉  | 236/297 [1:14:39<18:37, 18.32s/it, loss=0.0385]
Training:  79%|███████▉  | 236/297 [1:14:57<18:37, 18.32s/it, loss=0.0350]
Training:  80%|███████▉  | 237/297 [1:14:57<18:24, 18.40s/it, loss=0.0350]
Training:  80%|███████▉  | 237/297 [1:15:16<18:24, 18.40s/it, loss=0.0402]
Training:  80%|████████  | 238/297 [1:15:16<18:05, 18.40s/it, loss=0.0402]
Training:  80%|████████  | 238/297 [1:15:34<18:05, 18.40s/it, loss=0.0387]
Training:  80%|████████  | 239/297 [1:15:34<17:51, 18.47s/it, loss=0.0387]
Training:  80%|████████  | 239/297 [1:15:53<17:51, 18.47s/it, loss=0.0427]
Training:  81%|████████  | 240/297 [1:15:53<17:28, 18.39s/it, loss=0.0427]
Training:  81%|████████  | 240/297 [1:16:11<17:28, 18.39s/it, loss=0.0382]
Training:  81%|████████  | 241/297 [1:16:11<17:13, 18.46s/it, loss=0.0382]
Training:  81%|████████  | 241/297 [1:16:30<17:13, 18.46s/it, loss=0.0490]
Training:  81%|████████▏ | 242/297 [1:16:30<16:51, 18.39s/it, loss=0.0490]
Training:  81%|████████▏ | 242/297 [1:16:48<16:51, 18.39s/it, loss=0.0447]
Training:  82%|████████▏ | 243/297 [1:16:48<16:33, 18.39s/it, loss=0.0447]